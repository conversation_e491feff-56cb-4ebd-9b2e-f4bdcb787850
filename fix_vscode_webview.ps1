# VSCode Web视图Service Worker错误修复脚本
# 项老师AI工作室 - VSCode问题解决方案

Write-Host "=== VSCode Web视图错误修复工具 ===" -ForegroundColor Green
Write-Host "正在修复Service Worker注册失败问题..." -ForegroundColor Yellow

# 步骤1：关闭所有VSCode进程
Write-Host "`n步骤1：关闭VSCode进程..." -ForegroundColor Cyan
try {
    Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✓ VSCode进程已关闭" -ForegroundColor Green
} catch {
    Write-Host "✓ 没有运行中的VSCode进程" -ForegroundColor Green
}

# 步骤2：清理缓存目录
Write-Host "`n步骤2：清理缓存文件..." -ForegroundColor Cyan

$cachePaths = @(
    "$env:APPDATA\Code\CachedExtensions",
    "$env:APPDATA\Code\logs",
    "$env:APPDATA\Code\User\workspaceStorage",
    "$env:APPDATA\Code\User\globalStorage",
    "$env:TEMP\vscode-*"
)

foreach ($path in $cachePaths) {
    if (Test-Path $path) {
        try {
            Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "✓ 已清理: $path" -ForegroundColor Green
        } catch {
            Write-Host "⚠ 无法清理: $path" -ForegroundColor Yellow
        }
    } else {
        Write-Host "- 路径不存在: $path" -ForegroundColor Gray
    }
}

# 步骤3：清理浏览器缓存（针对Service Worker）
Write-Host "`n步骤3：清理Service Worker相关缓存..." -ForegroundColor Cyan

$serviceWorkerPaths = @(
    "$env:APPDATA\Code\User\CachedExtensionVSIXs",
    "$env:APPDATA\Code\User\History",
    "$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Service Worker"
)

foreach ($path in $serviceWorkerPaths) {
    if (Test-Path $path) {
        try {
            Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "✓ 已清理Service Worker缓存: $path" -ForegroundColor Green
        } catch {
            Write-Host "⚠ 无法清理: $path" -ForegroundColor Yellow
        }
    }
}

# 步骤4：重置VSCode设置（备份原设置）
Write-Host "`n步骤4：备份并重置设置..." -ForegroundColor Cyan

$settingsPath = "$env:APPDATA\Code\User\settings.json"
if (Test-Path $settingsPath) {
    $backupPath = "$env:APPDATA\Code\User\settings.json.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $settingsPath $backupPath
    Write-Host "✓ 设置已备份到: $backupPath" -ForegroundColor Green
}

# 步骤5：创建临时修复配置
Write-Host "`n步骤5：应用修复配置..." -ForegroundColor Cyan

$fixSettings = @{
    "extensions.autoUpdate" = $false
    "extensions.autoCheckUpdates" = $false
    "workbench.enableExperiments" = $false
    "telemetry.enableTelemetry" = $false
    "update.mode" = "none"
    "security.workspace.trust.enabled" = $false
} | ConvertTo-Json -Depth 10

Set-Content -Path $settingsPath -Value $fixSettings -Encoding UTF8
Write-Host "✓ 修复配置已应用" -ForegroundColor Green

# 步骤6：启动VSCode进行测试
Write-Host "`n步骤6：启动VSCode测试..." -ForegroundColor Cyan
Write-Host "正在启动VSCode（禁用扩展模式）..." -ForegroundColor Yellow

Start-Process "code" -ArgumentList "--disable-extensions", "--new-window" -NoNewWindow

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "如果问题仍然存在，请尝试以下额外步骤：" -ForegroundColor Yellow
Write-Host "1. 重启计算机" -ForegroundColor White
Write-Host "2. 以管理员身份运行VSCode" -ForegroundColor White
Write-Host "3. 逐个启用扩展以找出问题扩展" -ForegroundColor White
Write-Host "4. 考虑重新安装VSCode" -ForegroundColor White

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
