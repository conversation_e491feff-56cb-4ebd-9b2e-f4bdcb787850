# VSCode Web视图Service Worker错误修复指南

## 问题描述
VSCode出现错误：`Error: Could not register service worker: InvalidStateError: Failed to register a ServiceWorker: The document is in an invalid state.`

## 解决方案

### 方法一：手动清理缓存（推荐）

1. **完全关闭VSCode**
   - 关闭所有VSCode窗口
   - 按 `Ctrl+Shift+Esc` 打开任务管理器
   - 找到并结束所有 `Code.exe` 进程

2. **清理VSCode缓存目录**
   
   按 `Win+R` 打开运行对话框，依次输入以下路径并删除对应文件夹：
   
   ```
   %APPDATA%\Code\logs
   %APPDATA%\Code\CachedExtensions  
   %APPDATA%\Code\User\workspaceStorage
   %APPDATA%\Code\User\globalStorage
   ```

3. **备份并重置设置**
   
   - 打开路径：`%APPDATA%\Code\User\`
   - 备份 `settings.json` 文件（重命名为 `settings.json.backup`）
   - 创建新的 `settings.json` 文件，内容如下：

   ```json
   {
     "extensions.autoUpdate": false,
     "extensions.autoCheckUpdates": false,
     "workbench.enableExperiments": false,
     "telemetry.enableTelemetry": false,
     "update.mode": "none",
     "security.workspace.trust.enabled": false
   }
   ```

4. **以安全模式启动VSCode**
   
   按 `Win+R`，输入：
   ```
   code --disable-extensions --new-window
   ```

### 方法二：重新安装VSCode

如果方法一无效，建议：

1. **卸载VSCode**
   - 控制面板 → 程序和功能 → 卸载 Visual Studio Code
   - 删除残留文件夹：`%APPDATA%\Code` 和 `%LOCALAPPDATA%\Programs\Microsoft VS Code`

2. **重新下载安装**
   - 从官网下载最新版本：https://code.visualstudio.com/
   - 以管理员身份安装

### 方法三：扩展排查

如果问题仍然存在：

1. **逐个启用扩展**
   - 在安全模式下启动VSCode
   - 逐个启用扩展，找出问题扩展
   - 特别注意以下扩展：
     - augment.vscode-augment
     - menglong.cursor-infinity
     - xianglaoshi.cursor-ip

2. **更新或重装问题扩展**

### 方法四：系统级修复

1. **以管理员身份运行VSCode**
2. **重启计算机**
3. **检查Windows更新**
4. **运行系统文件检查**：
   ```cmd
   sfc /scannow
   ```

## 预防措施

1. **定期清理缓存**
2. **避免同时安装过多扩展**
3. **及时更新VSCode和扩展**
4. **使用稳定版本而非预览版**

## 技术原理

Service Worker错误通常由以下原因引起：
- 扩展冲突导致的状态异常
- 缓存文件损坏
- 权限问题
- 浏览器内核状态异常

## 联系支持

如果以上方法都无效，请：
1. 记录详细的错误信息
2. 查看VSCode开发者工具控制台
3. 到VSCode官方GitHub提交issue

---
*项老师AI工作室 - VSCode问题解决专家*
