@echo off
echo === VSCode Web视图错误修复工具 ===
echo 项老师AI工作室 - 正在修复Service Worker问题...
echo.

echo 步骤1：关闭VSCode进程...
taskkill /f /im Code.exe >nul 2>&1
if %errorlevel%==0 (
    echo ✓ VSCode进程已关闭
) else (
    echo ✓ 没有运行中的VSCode进程
)
echo.

echo 步骤2：清理缓存文件...
if exist "%APPDATA%\Code\logs" (
    rmdir /s /q "%APPDATA%\Code\logs" >nul 2>&1
    echo ✓ 已清理日志缓存
)

if exist "%APPDATA%\Code\CachedExtensions" (
    rmdir /s /q "%APPDATA%\Code\CachedExtensions" >nul 2>&1
    echo ✓ 已清理扩展缓存
)

if exist "%APPDATA%\Code\User\workspaceStorage" (
    rmdir /s /q "%APPDATA%\Code\User\workspaceStorage" >nul 2>&1
    echo ✓ 已清理工作区存储
)

if exist "%APPDATA%\Code\User\globalStorage" (
    rmdir /s /q "%APPDATA%\Code\User\globalStorage" >nul 2>&1
    echo ✓ 已清理全局存储
)
echo.

echo 步骤3：备份当前设置...
if exist "%APPDATA%\Code\User\settings.json" (
    copy "%APPDATA%\Code\User\settings.json" "%APPDATA%\Code\User\settings.json.backup" >nul 2>&1
    echo ✓ 设置已备份
)
echo.

echo 步骤4：创建修复配置...
echo { > "%APPDATA%\Code\User\settings.json"
echo   "extensions.autoUpdate": false, >> "%APPDATA%\Code\User\settings.json"
echo   "extensions.autoCheckUpdates": false, >> "%APPDATA%\Code\User\settings.json"
echo   "workbench.enableExperiments": false, >> "%APPDATA%\Code\User\settings.json"
echo   "telemetry.enableTelemetry": false, >> "%APPDATA%\Code\User\settings.json"
echo   "update.mode": "none", >> "%APPDATA%\Code\User\settings.json"
echo   "security.workspace.trust.enabled": false >> "%APPDATA%\Code\User\settings.json"
echo } >> "%APPDATA%\Code\User\settings.json"
echo ✓ 修复配置已创建
echo.

echo 步骤5：启动VSCode测试...
echo 正在以安全模式启动VSCode...
start "" code --disable-extensions --new-window
echo.

echo === 修复完成 ===
echo.
echo 如果问题仍然存在，请尝试：
echo 1. 重启计算机
echo 2. 以管理员身份运行VSCode
echo 3. 逐个启用扩展以找出问题扩展
echo 4. 考虑重新安装VSCode
echo.
echo 按任意键退出...
pause >nul
